#ifndef __LAP_CONTROL_H
#define __LAP_CONTROL_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 圈数控制模式定义 */
typedef enum {
    LAP_MODE_SETUP = 0,        /* 设置模式：LED快闪，等待按键设定圈数 */
    LAP_MODE_COUNTDOWN,        /* 倒计时模式：3秒等待后自动开始 */
    LAP_MODE_RUNNING,          /* 运行模式：循迹并计数圈数 */
    LAP_MODE_FINISHED          /* 完成模式：停车并指示完成 */
} lap_mode_t;

/* 赛道和编码器参数配置 */
#define TRACK_PERIMETER_CM      400.0f  /* 赛道周长 400cm (100×100正方形) */
#define TRACK_TOLERANCE_CM      20.0f   /* 允许误差 ±20cm */
#define TRACK_MIN_PERIMETER     380.0f  /* 最小周长 */
#define TRACK_MAX_PERIMETER     420.0f  /* 最大周长 */

/* 编码器参数（基于现有硬件） */
#define ENCODER_PPR             13      /* 每转脉冲数 */
#define GEAR_RATIO              30      /* 减速比 */
#define WHEEL_DIAMETER_MM       67      /* 轮径67mm */
#define WHEEL_PERIMETER_MM      210.4867f /* 轮周长 */

/* 时间参数配置 */
#define SETUP_TIMEOUT_MS        1000    /* 设置超时时间 1秒 */
#define COUNTDOWN_TIME_MS       3000    /* 倒计时时间 3秒 */

/* 圈数范围限制 */
#define MIN_LAP_COUNT           1       /* 最小圈数 */
#define MAX_LAP_COUNT           5       /* 最大圈数 */

/* 圈数计算器结构体 */
typedef struct {
    int32_t total_encoder_count;    /* 总编码器计数 */
    float distance_cm;              /* 行驶距离(cm) */
    uint8_t completed_laps;         /* 完成圈数 */
    float current_lap_progress;     /* 当前圈进度(0.0-1.0) */
    float average_lap_distance;     /* 平均每圈距离(用于自适应) */
} lap_counter_t;

/* 圈数控制结构体 */
typedef struct {
    lap_mode_t mode;                /* 当前模式 */
    uint8_t target_laps;            /* 目标圈数 (1-5) */
    uint8_t current_laps;           /* 当前完成圈数 */
    uint32_t mode_start_time;       /* 模式开始时间 */
    uint32_t last_click_time;       /* 最后点击时间 */
    int32_t start_encoder_count;    /* 起始编码器计数 */
    lap_counter_t counter;          /* 圈数计算器 */
    bool is_initialized;            /* 初始化标志 */
} lap_control_t;

/* 圈数控制模块接口函数 */

/**
 * @brief 初始化圈数控制模块
 * @note 设置初始状态为设置模式，目标圈数为1
 */
void lap_control_init(void);

/**
 * @brief 处理圈数控制逻辑
 * @param current_time 当前系统时间戳(ms)
 * @note 需要在主循环中定期调用，处理状态机和按键事件
 */
void lap_control_process(uint32_t current_time);

/**
 * @brief 更新编码器数据并计算圈数
 * @param encoder_a_count 编码器A计数
 * @param encoder_b_count 编码器B计数
 * @note 在编码器数据更新时调用
 */
void lap_control_update_encoder(int32_t encoder_a_count, int32_t encoder_b_count);

/**
 * @brief 获取当前模式
 * @return lap_mode_t 当前运行模式
 */
lap_mode_t lap_control_get_mode(void);

/**
 * @brief 获取目标圈数
 * @return uint8_t 设定的目标圈数 (1-5)
 */
uint8_t lap_control_get_target_laps(void);

/**
 * @brief 获取当前完成圈数
 * @return uint8_t 已完成的圈数
 */
uint8_t lap_control_get_current_laps(void);

/**
 * @brief 获取当前圈进度
 * @return float 当前圈完成进度 (0.0-1.0)
 */
float lap_control_get_current_progress(void);

/**
 * @brief 获取总行驶距离
 * @return float 总行驶距离(cm)
 */
float lap_control_get_total_distance(void);

/**
 * @brief 检查是否已完成所有圈数
 * @return bool true: 已完成, false: 未完成
 */
bool lap_control_is_finished(void);

/**
 * @brief 强制停止并进入完成模式
 * @note 用于紧急停止或手动结束
 */
void lap_control_force_finish(void);

/**
 * @brief 重置圈数控制状态
 * @note 重新开始设置，用于重新配置
 */
void lap_control_reset(void);

/**
 * @brief 设置起始编码器计数值
 * @param encoder_a_count 编码器A当前计数
 * @param encoder_b_count 编码器B当前计数
 * @note 在进入运行模式时调用，设置计数基准
 */
void lap_control_set_start_encoder(int32_t encoder_a_count, int32_t encoder_b_count);

#ifdef __cplusplus
}
#endif

#endif /* __LAP_CONTROL_H */
