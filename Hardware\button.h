#ifndef __BUTTON_H
#define __BUTTON_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>
#include "../ti_msp_dl_config.h"
#include "../ti_msp_dl_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 按键事件类型定义 */
typedef enum {
    BTN_EVENT_NONE = 0,        /* 无事件 */
    BTN_EVENT_SHORT_CLICK      /* 短按事件 */
} btn_event_t;

/* 按键状态定义 */
typedef enum {
    BTN_STATE_IDLE = 0,        /* 空闲状态 */
    BTN_STATE_PRESSED,         /* 按下状态 */
    BTN_STATE_DEBOUNCE,        /* 防抖状态 */
    BTN_STATE_VALID_PRESS      /* 有效按下状态 */
} btn_state_t;

/* 按键时间参数配置 */
#define BTN_DEBOUNCE_TIME       20      /* 防抖时间 20ms */
#define BTN_SHORT_CLICK_MIN     50      /* 短按最小时间 50ms */
#define BTN_SHORT_CLICK_MAX     500     /* 短按最大时间 500ms */

/* 按键GPIO配置 - 使用现有的UserKEY */
#define BUTTON_PORT             KEY_PORT
#define BUTTON_PIN              KEY_UserKEY_PIN

/* 按键结构体定义 */
typedef struct {
    btn_state_t state;          /* 当前状态 */
    uint32_t press_time;        /* 按下时间戳 */
    uint32_t release_time;      /* 释放时间戳 */
    uint32_t last_time;         /* 上次处理时间 */
    bool debounce_flag;         /* 防抖标志 */
    bool last_pin_state;        /* 上次引脚状态 */
} button_t;

/* 按键模块接口函数 */

/**
 * @brief 初始化按键模块
 * @note 配置GPIO为输入模式，启用内部上拉电阻
 */
void button_init(void);

/**
 * @brief 处理按键状态检测
 * @param current_time 当前系统时间戳(ms)
 * @return btn_event_t 检测到的按键事件
 * @note 需要在主循环中定期调用，建议调用频率 >= 100Hz
 */
btn_event_t button_process(uint32_t current_time);

/**
 * @brief 读取按键引脚状态
 * @return true: 按键按下, false: 按键释放
 * @note 内部函数，处理硬件电平逻辑
 */
bool button_read_pin(void);

/**
 * @brief 获取按键当前状态
 * @return btn_state_t 当前按键状态
 * @note 用于调试和状态查询
 */
btn_state_t button_get_state(void);

/**
 * @brief 重置按键状态
 * @note 用于系统初始化或异常恢复
 */
void button_reset(void);

#ifdef __cplusplus
}
#endif

#endif /* __BUTTON_H */
