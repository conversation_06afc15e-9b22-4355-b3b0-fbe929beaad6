# 八路灰度循迹功能集成指南

## 📋 项目概述

**项目名称**: 两轮驱动三轮车八路灰度循迹系统  
**赛道规格**: 100cm × 100cm 正方形赛道  
**小车配置**: 后轮驱动，前轮万向轮，灰度传感器安装在万向轮附近  
**控制方式**: 直接PWM控制（已移除PID算法）

## 🔌 完整接线说明

### **TB6612电机驱动模块接线**
```
电源接线：
- VCC → 5V电源正极
- GND → 电源地线
- STBY → 3.3V（使能引脚，必须接高电平）

PWM信号接线：
- PWMA → PB2（左电机PWM控制）
- PWMB → PB3（右电机PWM控制）

方向控制接线：
- AIN1 → PA16（左电机方向控制1）
- AIN2 → PA17（左电机方向控制2）
- BIN1 → PA14（右电机方向控制1）
- BIN2 → PA13（右电机方向控制2）

电机输出接线：
- AO1、AO2 → 左电机正负极
- BO1、BO2 → 右电机正负极
```

### **八路灰度传感器接线**
```
电源接线：
- VCC → 3.3V或5V电源（根据传感器规格）
- GND → 电源地线

信号接线（从左到右）：
- OUT1 → PA9  (HDPIN_0，最左侧传感器)
- OUT2 → PA27 (HDPIN_1，左侧传感器)
- OUT3 → PA24 (HDPIN_2，左中传感器)
- OUT4 → PB16 (HDPIN_3，中心左传感器)
- OUT5 → PA12 (HDPIN_4，中心右传感器)
- OUT6 → PB6  (HDPIN_5，右中传感器)
- OUT7 → PB7  (HDPIN_6，右侧传感器)
- OUT8 → 需要添加GPIO引脚（最右侧传感器，可选）

注意：传感器安装在万向轮附近，确保能够检测到地面黑线
```

### **编码电机接线**
```
编码器信号：
- 编码器A相 → 编码器中断引脚（具体引脚见配置文件）
- 编码器B相 → 编码器中断引脚（具体引脚见配置文件）

电机电源：
- 电机正负极 → 通过TB6612驱动模块连接
```

### **系统指示接线**
```
状态指示：
- LED → PB9（系统运行状态指示）

用户输入（可选）：
- 按键 → PA8（KEY0，用于启动/停止控制）
```

## 🎯 循迹算法说明

### **传感器权重分配**
```
HDPIN_0 (最左): +15  （强左转）
HDPIN_1 (左侧): +10  （左转）
HDPIN_2 (左中): +4   （微左转）
HDPIN_3 (中左): -4   （微右转）
HDPIN_4 (中右): -10  （右转）
HDPIN_5 (右侧): -15  （强右转）
HDPIN_6 (右侧): 用于路口检测
```

### **控制逻辑**
```
1. 读取所有灰度传感器状态
2. 根据权重计算转向值 (score)
3. 基础速度 = 3000 PWM
4. 左电机速度 = 基础速度 - (转向值 × 20)
5. 右电机速度 = 基础速度 + (转向值 × 20)
6. PWM限制在 1000-6000 范围内
```

### **特殊功能**
```
路口检测：
- HDPIN_5检测到黑线时计数
- 用于识别路口或特殊标记
- 可用于圈数统计或路径选择
```

## 🚀 代码集成状态

### **已完成功能**
- ✅ 八路灰度传感器GPIO配置
- ✅ xunji()循迹算法函数
- ✅ 直接PWM电机控制
- ✅ 10ms循迹控制循环
- ✅ 转向系数调整机制

### **控制参数**
```c
基础前进速度: 3000 PWM
转向系数: 20
控制频率: 10ms (100Hz)
PWM范围: 1000-6000
LED指示频率: 约1Hz
```

## 🔧 调试与优化

### **参数调整**
```c
// 在empty.c中可调整的参数：
int base_speed = 3000;        // 基础速度（1000-6000）
int turn_factor = 20;         // 转向系数（10-50）
int control_period = 10000;   // 控制周期（微秒）
```

### **性能优化建议**
1. **速度调整**: 根据赛道情况调整base_speed
2. **转向灵敏度**: 调整turn_factor改变转向响应
3. **控制频率**: 调整control_period改变响应速度
4. **传感器阈值**: 在xunji()函数中调整检测阈值

## 📊 测试验证

### **功能测试清单**
- [ ] LED正常闪烁（系统运行正常）
- [ ] 灰度传感器能检测黑线
- [ ] 小车能沿直线前进
- [ ] 遇到黑线能正确转向
- [ ] 转向后能回到直线
- [ ] 能完成100cm×100cm方形赛道

### **故障排除**
```
问题：小车不动
解决：检查TB6612 STBY引脚是否接3.3V

问题：转向不灵敏
解决：增大turn_factor值

问题：转向过度
解决：减小turn_factor值或base_speed

问题：传感器无响应
解决：检查传感器电源和信号线连接
```

## 🎯 下一步扩展

### **可选功能**
- 添加第8路传感器（HDPIN_7）
- 集成编码器速度反馈
- 添加路口计数显示
- 实现多圈循迹记录

### **性能提升**
- 动态速度调整
- 预测性转向控制
- 路径优化算法
- 自适应参数调整

## ✅ 总结

当前系统已具备完整的八路灰度循迹功能：
- 硬件接线完整清晰
- 软件算法集成完成
- 控制参数可调优化
- 适配100cm×100cm方形赛道

**系统已准备就绪，可立即进行循迹测试！**
