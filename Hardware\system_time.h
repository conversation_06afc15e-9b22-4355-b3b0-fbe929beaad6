#ifndef __SYSTEM_TIME_H
#define __SYSTEM_TIME_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化系统时间模块
 * @note 初始化时间基准，重置计数器
 */
void system_time_init(void);

/**
 * @brief 更新系统时间
 * @note 需要在主循环中定期调用，用于维护时间基准
 */
void system_time_update(void);

/**
 * @brief 获取系统时间戳
 * @return uint32_t 当前系统时间戳(毫秒)
 * @note 提供毫秒级精度的时间戳
 */
uint32_t system_time_get_ms(void);

/**
 * @brief 获取系统运行时间
 * @return uint32_t 系统运行时间(秒)
 */
uint32_t system_time_get_seconds(void);

/**
 * @brief 时间差计算
 * @param start_time 起始时间戳
 * @param end_time 结束时间戳
 * @return uint32_t 时间差(毫秒)
 * @note 处理时间戳溢出情况
 */
uint32_t system_time_diff_ms(uint32_t start_time, uint32_t end_time);

/**
 * @brief 延时函数
 * @param delay_ms 延时时间(毫秒)
 * @note 阻塞式延时，谨慎使用
 */
void system_time_delay_ms(uint32_t delay_ms);

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_TIME_H */
