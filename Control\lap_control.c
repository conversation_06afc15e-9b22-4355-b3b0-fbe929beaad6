#include "lap_control.h"
#include "../Hardware/button.h"

/* 圈数控制实例 - 静态变量确保封装性 */
static lap_control_t g_lap_control = {0};

/* 内部辅助函数声明 */
static float encoder_to_distance_cm(int32_t encoder_count);
static uint8_t calculate_completed_laps(float distance_cm);
static void handle_setup_mode(uint32_t current_time);
static void handle_countdown_mode(uint32_t current_time);
static void handle_running_mode(uint32_t current_time);
static void handle_finished_mode(uint32_t current_time);

/**
 * @brief 初始化圈数控制模块
 */
void lap_control_init(void)
{
    /* 重置所有状态 */
    lap_control_reset();
    
    /* 设置初始模式为设置模式 */
    g_lap_control.mode = LAP_MODE_SETUP;
    g_lap_control.target_laps = 1;  /* 默认1圈 */
    g_lap_control.is_initialized = true;
}

/**
 * @brief 处理圈数控制逻辑
 */
void lap_control_process(uint32_t current_time)
{
    if(!g_lap_control.is_initialized) {
        return;
    }
    
    /* 根据当前模式处理逻辑 */
    switch(g_lap_control.mode) {
        case LAP_MODE_SETUP:
            handle_setup_mode(current_time);
            break;
            
        case LAP_MODE_COUNTDOWN:
            handle_countdown_mode(current_time);
            break;
            
        case LAP_MODE_RUNNING:
            handle_running_mode(current_time);
            break;
            
        case LAP_MODE_FINISHED:
            handle_finished_mode(current_time);
            break;
            
        default:
            /* 异常状态，重置 */
            lap_control_reset();
            break;
    }
}

/**
 * @brief 更新编码器数据并计算圈数
 */
void lap_control_update_encoder(int32_t encoder_a_count, int32_t encoder_b_count)
{
    if(g_lap_control.mode != LAP_MODE_RUNNING) {
        return;
    }
    
    /* 计算平均编码器计数（左右轮平均） */
    int32_t current_count = (encoder_a_count + encoder_b_count) / 2;
    int32_t relative_count = current_count - g_lap_control.start_encoder_count;
    
    /* 转换为行驶距离 */
    float distance_cm = encoder_to_distance_cm(relative_count);
    g_lap_control.counter.distance_cm = distance_cm;
    g_lap_control.counter.total_encoder_count = relative_count;
    
    /* 计算完成圈数 */
    uint8_t completed = calculate_completed_laps(distance_cm);
    
    /* 检查是否完成目标圈数 */
    if(completed >= g_lap_control.target_laps) {
        g_lap_control.current_laps = g_lap_control.target_laps;
        g_lap_control.mode = LAP_MODE_FINISHED;
    } else {
        g_lap_control.current_laps = completed;
    }
    
    /* 计算当前圈进度 */
    float lap_distance = distance_cm - (completed * TRACK_PERIMETER_CM);
    g_lap_control.counter.current_lap_progress = lap_distance / TRACK_PERIMETER_CM;
    
    /* 限制进度范围 */
    if(g_lap_control.counter.current_lap_progress > 1.0f) {
        g_lap_control.counter.current_lap_progress = 1.0f;
    }
    if(g_lap_control.counter.current_lap_progress < 0.0f) {
        g_lap_control.counter.current_lap_progress = 0.0f;
    }
}

/* ==================== 内部辅助函数实现 ==================== */

/**
 * @brief 编码器计数转换为距离
 */
static float encoder_to_distance_cm(int32_t encoder_count)
{
    /* 距离 = (编码器计数 / (PPR × 减速比 × 2倍频)) × 轮周长 */
    float distance_mm = (float)encoder_count / (ENCODER_PPR * GEAR_RATIO * 2.0f) * WHEEL_PERIMETER_MM;
    return distance_mm / 10.0f;  /* 转换为cm */
}

/**
 * @brief 计算完成圈数
 */
static uint8_t calculate_completed_laps(float distance_cm)
{
    if(distance_cm < 0.0f) return 0;
    return (uint8_t)(distance_cm / TRACK_PERIMETER_CM);
}

/**
 * @brief 处理设置模式
 */
static void handle_setup_mode(uint32_t current_time)
{
    /* 处理按键事件 */
    btn_event_t event = button_process(current_time);

    if(event == BTN_EVENT_SHORT_CLICK) {
        /* 圈数累加 (1-5循环) */
        g_lap_control.target_laps++;
        if(g_lap_control.target_laps > MAX_LAP_COUNT) {
            g_lap_control.target_laps = MIN_LAP_COUNT;
        }

        /* 更新最后点击时间 */
        g_lap_control.last_click_time = current_time;

        /* TODO: 更新LED指示 - 显示当前圈数 */
        /* led_show_lap_count(g_lap_control.target_laps); */
    }

    /* 检查3秒超时 */
    if(g_lap_control.last_click_time > 0 &&
       (current_time - g_lap_control.last_click_time) >= SETUP_TIMEOUT_MS) {
        /* 进入倒计时模式 */
        g_lap_control.mode = LAP_MODE_COUNTDOWN;
        g_lap_control.mode_start_time = current_time;

        /* TODO: 更新LED指示 - 倒计时模式 */
        /* led_set_countdown_mode(); */
    }
}

/**
 * @brief 处理倒计时模式
 */
static void handle_countdown_mode(uint32_t current_time)
{
    /* 检查倒计时是否结束 */
    if((current_time - g_lap_control.mode_start_time) >= COUNTDOWN_TIME_MS) {
        /* 进入运行模式 */
        g_lap_control.mode = LAP_MODE_RUNNING;
        g_lap_control.mode_start_time = current_time;

        /* 起始编码器计数将在主程序中通过lap_control_set_start_encoder()设置 */
        /* 这里先设为0，主程序会在模式切换时更新 */
        g_lap_control.start_encoder_count = 0;

        /* 重置计数器 */
        g_lap_control.counter.distance_cm = 0.0f;
        g_lap_control.counter.completed_laps = 0;
        g_lap_control.counter.current_lap_progress = 0.0f;
        g_lap_control.current_laps = 0;

        /* TODO: 更新LED指示 - 运行模式 */
        /* led_set_running_mode(); */
    }
}

/**
 * @brief 处理运行模式
 */
static void handle_running_mode(uint32_t current_time)
{
    /* 运行模式下主要通过lap_control_update_encoder()更新状态 */
    /* 这里可以添加运行时的额外逻辑，如超时检测等 */

    /* TODO: 可以添加单圈时间监控 */
    /* 如果单圈时间超过20秒，可以记录或报警 */
}

/**
 * @brief 处理完成模式
 */
static void handle_finished_mode(uint32_t current_time)
{
    /* 完成模式下保持状态，等待重置 */
    /* TODO: 更新LED指示 - 完成模式 */
    /* led_set_finished_mode(); */
}

/* ==================== 公共接口函数实现 ==================== */

/**
 * @brief 获取当前模式
 */
lap_mode_t lap_control_get_mode(void)
{
    return g_lap_control.mode;
}

/**
 * @brief 获取目标圈数
 */
uint8_t lap_control_get_target_laps(void)
{
    return g_lap_control.target_laps;
}

/**
 * @brief 获取当前完成圈数
 */
uint8_t lap_control_get_current_laps(void)
{
    return g_lap_control.current_laps;
}

/**
 * @brief 获取当前圈进度
 */
float lap_control_get_current_progress(void)
{
    return g_lap_control.counter.current_lap_progress;
}

/**
 * @brief 获取总行驶距离
 */
float lap_control_get_total_distance(void)
{
    return g_lap_control.counter.distance_cm;
}

/**
 * @brief 检查是否已完成所有圈数
 */
bool lap_control_is_finished(void)
{
    return (g_lap_control.mode == LAP_MODE_FINISHED);
}

/**
 * @brief 强制停止并进入完成模式
 */
void lap_control_force_finish(void)
{
    g_lap_control.mode = LAP_MODE_FINISHED;
    g_lap_control.current_laps = g_lap_control.target_laps;
}

/**
 * @brief 重置圈数控制状态
 */
void lap_control_reset(void)
{
    g_lap_control.mode = LAP_MODE_SETUP;
    g_lap_control.target_laps = 1;
    g_lap_control.current_laps = 0;
    g_lap_control.mode_start_time = 0;
    g_lap_control.last_click_time = 0;
    g_lap_control.start_encoder_count = 0;

    /* 重置计数器 */
    g_lap_control.counter.total_encoder_count = 0;
    g_lap_control.counter.distance_cm = 0.0f;
    g_lap_control.counter.completed_laps = 0;
    g_lap_control.counter.current_lap_progress = 0.0f;
    g_lap_control.counter.average_lap_distance = TRACK_PERIMETER_CM;

    g_lap_control.is_initialized = false;
}

/**
 * @brief 设置起始编码器计数值
 */
void lap_control_set_start_encoder(int32_t encoder_a_count, int32_t encoder_b_count)
{
    g_lap_control.start_encoder_count = (encoder_a_count + encoder_b_count) / 2;
}
