#include "button.h"

/* 按键实例 - 静态变量确保封装性 */
static button_t g_button = {0};

/**
 * @brief 初始化按键模块
 */
void button_init(void)
{
    /* 重置按键状态 */
    button_reset();
    
    /* GPIO配置在ti_msp_dl_config.c中完成，这里只做软件初始化 */
    /* 如果需要动态配置GPIO，可以在这里添加 */
    
    /* 读取初始引脚状态 */
    g_button.last_pin_state = button_read_pin();
}

/**
 * @brief 读取按键引脚状态
 */
bool button_read_pin(void)
{
    /* 按键按下时GPIO读取为低电平（假设使用上拉电阻） */
    /* 返回true表示按键按下，false表示按键释放 */
    return !DL_GPIO_readPins(BUTTON_PORT, BUTTON_PIN);
}

/**
 * @brief 处理按键状态检测
 */
btn_event_t button_process(uint32_t current_time)
{
    btn_event_t event = BTN_EVENT_NONE;
    bool current_pin_state = button_read_pin();
    
    /* 检测引脚状态变化 */
    bool state_changed = (current_pin_state != g_button.last_pin_state);
    
    switch(g_button.state) {
        case BTN_STATE_IDLE:
            if(current_pin_state && state_changed) {
                /* 检测到按键按下 */
                g_button.state = BTN_STATE_PRESSED;
                g_button.press_time = current_time;
                g_button.debounce_flag = false;
            }
            break;
            
        case BTN_STATE_PRESSED:
            if(!current_pin_state && state_changed) {
                /* 按键释放，但可能是抖动 */
                g_button.state = BTN_STATE_DEBOUNCE;
                g_button.release_time = current_time;
            }
            else if(current_pin_state) {
                /* 按键持续按下，检查防抖时间 */
                if(!g_button.debounce_flag && 
                   (current_time - g_button.press_time) >= BTN_DEBOUNCE_TIME) {
                    g_button.debounce_flag = true;
                    g_button.state = BTN_STATE_VALID_PRESS;
                }
            }
            break;
            
        case BTN_STATE_DEBOUNCE:
            if(current_pin_state && state_changed) {
                /* 抖动，重新按下 */
                g_button.state = BTN_STATE_PRESSED;
                g_button.press_time = current_time;
                g_button.debounce_flag = false;
            }
            else if(!current_pin_state) {
                /* 持续释放，检查防抖时间 */
                if((current_time - g_button.release_time) >= BTN_DEBOUNCE_TIME) {
                    /* 确认释放，检查是否为有效短按 */
                    uint32_t press_duration = g_button.release_time - g_button.press_time;
                    
                    if(g_button.debounce_flag && 
                       press_duration >= BTN_SHORT_CLICK_MIN && 
                       press_duration <= BTN_SHORT_CLICK_MAX) {
                        event = BTN_EVENT_SHORT_CLICK;
                    }
                    
                    /* 返回空闲状态 */
                    g_button.state = BTN_STATE_IDLE;
                    g_button.debounce_flag = false;
                }
            }
            break;
            
        case BTN_STATE_VALID_PRESS:
            if(!current_pin_state && state_changed) {
                /* 有效按下后释放 */
                g_button.state = BTN_STATE_DEBOUNCE;
                g_button.release_time = current_time;
            }
            else if(current_pin_state) {
                /* 检查是否超过短按最大时间 */
                if((current_time - g_button.press_time) > BTN_SHORT_CLICK_MAX) {
                    /* 超时，不算短按，直接等待释放 */
                    g_button.debounce_flag = false;
                }
            }
            break;
            
        default:
            /* 异常状态，重置 */
            button_reset();
            break;
    }
    
    /* 更新状态 */
    g_button.last_pin_state = current_pin_state;
    g_button.last_time = current_time;
    
    return event;
}

/**
 * @brief 获取按键当前状态
 */
btn_state_t button_get_state(void)
{
    return g_button.state;
}

/**
 * @brief 重置按键状态
 */
void button_reset(void)
{
    g_button.state = BTN_STATE_IDLE;
    g_button.press_time = 0;
    g_button.release_time = 0;
    g_button.last_time = 0;
    g_button.debounce_flag = false;
    g_button.last_pin_state = false;
}
