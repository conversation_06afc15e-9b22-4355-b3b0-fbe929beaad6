# 圈数控制系统集成指南

## 🎯 系统概述

本系统实现了基于按键输入和编码器计数的智能圈数控制功能，满足竞赛要求的1-5圈可设定循迹需求。

### **核心特性**
- ✅ 按键设定圈数（1-5圈循环）
- ✅ 3秒自动确认机制
- ✅ 基于编码器的精确圈数计算
- ✅ 低耦合模块化设计
- ✅ 完整的状态机控制

## 🏗️ 系统架构

### **模块结构**
```
Hardware/
├── button.h/c          # 按键硬件抽象层
├── system_time.h/c     # 时间系统适配层
Control/
├── lap_control.h/c     # 圈数控制业务逻辑
empty.c                 # 主程序集成
```

### **状态机流程**
```
开机 → 设置模式 → 倒计时模式 → 运行模式 → 完成模式
  ↓        ↓          ↓          ↓         ↓
LED快闪   按键设定    3秒等待     循迹计数   停车完成
```

## ⚙️ 核心参数配置

### **按键参数**
```c
#define BTN_DEBOUNCE_TIME       20      // 防抖时间 20ms
#define BTN_SHORT_CLICK_MIN     50      // 短按最小时间 50ms
#define BTN_SHORT_CLICK_MAX     500     // 短按最大时间 500ms
```

### **赛道参数**
```c
#define TRACK_PERIMETER_CM      400.0f  // 赛道周长 400cm
#define TRACK_TOLERANCE_CM      20.0f   // 允许误差 ±20cm
```

### **编码器参数**
```c
#define ENCODER_PPR             13      // 每转脉冲数
#define GEAR_RATIO              30      // 减速比
#define WHEEL_DIAMETER_MM       67      // 轮径67mm
#define WHEEL_PERIMETER_MM      210.4867f // 轮周长
```

### **时间参数**
```c
#define SETUP_TIMEOUT_MS        3000    // 设置超时 3秒
#define COUNTDOWN_TIME_MS       3000    // 倒计时 3秒
```

## 🔧 使用方法

### **操作流程**
1. **开机**：系统进入设置模式，LED快速闪烁
2. **设定圈数**：短按按键，圈数累加（1→2→3→4→5→1）
3. **自动确认**：3秒无操作后自动进入倒计时
4. **开始循迹**：倒计时3秒后自动开始循迹
5. **自动停车**：完成设定圈数后自动停止

### **API接口**
```c
// 初始化
void button_init(void);
void system_time_init(void);
void lap_control_init(void);

// 主循环调用
void system_time_update(void);
void lap_control_process(uint32_t current_time);
void lap_control_update_encoder(int32_t encoder_a, int32_t encoder_b);

// 状态查询
lap_mode_t lap_control_get_mode(void);
uint8_t lap_control_get_target_laps(void);
uint8_t lap_control_get_current_laps(void);
float lap_control_get_current_progress(void);
```

## 🔌 硬件配置

### **按键连接**
```c
#define BUTTON_PORT             GPIOB_PORT
#define BUTTON_PIN              DL_GPIO_PIN_0
```
- 按键一端接GPIO引脚
- 另一端接GND
- 使用内部上拉电阻
- 按下时GPIO读取为低电平

### **编码器接口**
- 使用现有的`g_EncoderACount`和`g_EncoderBCount`
- 自动计算左右轮平均值
- 支持2倍频编码器

## 📊 圈数计算原理

### **距离转换公式**
```c
距离(cm) = 编码器计数 / (PPR × 减速比 × 2倍频) × 轮周长(mm) / 10
```

### **圈数计算**
```c
完成圈数 = 总距离(cm) / 赛道周长(cm)
当前圈进度 = (总距离 - 完成圈数×周长) / 周长
```

## 🚀 集成步骤

### **第一步：添加头文件**
```c
#include "Hardware/button.h"
#include "Hardware/system_time.h"
#include "Control/lap_control.h"
```

### **第二步：初始化模块**
```c
int main(void) {
    SYSCFG_DL_init();
    
    // 初始化新模块
    system_time_init();
    button_init();
    lap_control_init();
    
    // ... 其他初始化
}
```

### **第三步：主循环集成**
```c
while(1) {
    // 更新时间系统
    system_time_update();
    uint32_t current_time = system_time_get_ms();
    
    // 处理圈数控制
    lap_control_process(current_time);
    lap_control_update_encoder(g_EncoderACount, g_EncoderBCount);
    
    // 条件循迹
    if(lap_control_get_mode() == LAP_MODE_RUNNING) {
        // 原有循迹代码
    }
    else if(lap_control_get_mode() == LAP_MODE_FINISHED) {
        // 停止电机
    }
}
```

## 🛡️ 安全特性

### **防抖机制**
- 按键按下和释放都有20ms防抖
- 避免机械抖动导致的误触发

### **参数限制**
- 圈数限制在1-5范围内
- PWM输出限制在安全范围
- 时间戳溢出处理

### **异常恢复**
- 状态机异常时自动重置
- 编码器数据异常检测
- 超时保护机制

## 🔍 调试功能

### **状态查询**
```c
// 获取当前状态
lap_mode_t mode = lap_control_get_mode();
uint8_t target = lap_control_get_target_laps();
uint8_t current = lap_control_get_current_laps();
float progress = lap_control_get_current_progress();
```

### **调试输出**
- 可以通过UART输出状态信息
- LED指示当前模式
- 按键状态可查询

## ⚡ 性能优化

### **时间精度**
- 基于主循环计数的毫秒级时间戳
- 可根据实际循环频率调整精度

### **计算优化**
- 使用整数运算减少浮点计算
- 编码器数据缓存避免重复计算

### **内存使用**
- 静态变量封装，避免动态分配
- 结构体紧凑设计，减少内存占用

## 📝 注意事项

### **编译配置**
- 确保所有新文件添加到编译列表
- 检查头文件路径配置

### **GPIO配置**
- 按键GPIO需要在ti_msp_dl_config.c中配置
- 确保引脚不与其他功能冲突

### **时间基准**
- 系统时间基于主循环频率估算
- 可根据实际测试调整时间参数

## ✅ 测试验证

### **功能测试**
- [ ] 按键响应正常
- [ ] 圈数设定正确（1-5循环）
- [ ] 3秒超时确认
- [ ] 编码器计数准确
- [ ] 循迹控制正常
- [ ] 自动停车功能

### **性能测试**
- [ ] 单圈时间 ≤ 20秒
- [ ] 圈数计算精度 ± 5%
- [ ] 按键响应时间 < 100ms
- [ ] 系统稳定运行 > 5分钟

## 🎯 总结

本系统成功实现了低耦合的圈数控制功能：
- **模块化设计**：各模块职责清晰，便于维护
- **精确计算**：基于编码器的可靠圈数检测
- **用户友好**：简单的按键操作和LED指示
- **高可靠性**：完善的防抖和异常处理机制

**系统已准备就绪，可立即进行功能测试！**
