# 按键接线指南

## 🔌 **硬件连接方案**

### **方案一：使用现有UserKEY引脚（推荐）**

**引脚信息：**
- **GPIO引脚**：`GPIOA.18` 
- **物理引脚**：Package Pin 11
- **引脚名称**：UserKEY
- **配置**：内部上拉电阻已启用

**接线方法：**
```
按键一端 ──────── GPIOA.18 (Package Pin 11)
按键另一端 ────── GND
```

**电路原理：**
- 按键未按下：GPIO读取为高电平（3.3V）
- 按键按下：GPIO读取为低电平（0V）
- 内部上拉电阻确保稳定的高电平

### **方案二：自定义GPIO引脚**

如果需要使用其他引脚，可以选择以下空闲的GPIO：

**可选引脚：**
- `GPIOB.0` - Package Pin 12
- `GPIOB.1` - Package Pin 13  
- `GPIOB.4` - Package Pin 16
- `GPIOB.5` - Package Pin 17

**配置步骤：**
1. 修改 `Hardware/button.h` 中的引脚定义
2. 在 `ti_msp_dl_config.c` 中配置GPIO为输入模式
3. 启用内部上拉电阻

## ⚡ **当前系统配置**

**代码配置：**
```c
// Hardware/button.h
#define BUTTON_PORT             KEY_PORT        // GPIOA
#define BUTTON_PIN              KEY_UserKEY_PIN // DL_GPIO_PIN_18
```

**硬件状态：**
- ✅ 使用现有UserKEY引脚（GPIOA.18）
- ✅ 内部上拉电阻已启用
- ✅ 防抖时间：20ms
- ✅ 短按检测：50-500ms

## 🔧 **接线实施步骤**

### **第一步：准备材料**
- 轻触按键开关 × 1
- 杜邦线（公对母）× 2
- 面包板（可选）

### **第二步：物理连接**
1. **找到引脚**：在开发板上找到Package Pin 11（GPIOA.18）
2. **连接按键**：
   ```
   按键引脚1 ──── 杜邦线 ──── GPIOA.18 (Pin 11)
   按键引脚2 ──── 杜邦线 ──── GND
   ```
3. **检查连接**：确保连接牢固，无短路

### **第三步：功能测试**
1. **编译代码**：确保新添加的文件编译成功
2. **下载程序**：将程序下载到开发板
3. **测试按键**：
   - 开机后LED应快速闪烁（设置模式）
   - 短按按键，圈数应累加（1→2→3→4→5→1）
   - 3秒无操作后自动开始循迹

## 🛡️ **安全注意事项**

### **电气安全**
- ✅ 确保按键额定电压 ≥ 3.3V
- ✅ 避免短路GPIO引脚到电源
- ✅ 使用防静电措施

### **机械安全**
- ✅ 按键安装牢固，避免松动
- ✅ 导线长度适中，避免缠绕
- ✅ 预留足够的操作空间

## 🔍 **故障排除**

### **按键无响应**
**可能原因：**
- 接线错误或松动
- 按键损坏
- GPIO配置错误

**解决方法：**
1. 检查接线是否正确
2. 用万用表测试按键通断
3. 检查代码中的GPIO定义

### **误触发或抖动**
**可能原因：**
- 按键质量差
- 环境干扰
- 防抖时间不足

**解决方法：**
1. 更换质量更好的按键
2. 增加硬件滤波电容（可选）
3. 调整软件防抖时间

### **LED指示异常**
**可能原因：**
- 时间系统异常
- 状态机错误
- LED硬件问题

**解决方法：**
1. 检查系统时间更新
2. 调试状态机逻辑
3. 测试LED硬件功能

## 📊 **性能参数**

### **按键响应特性**
- **防抖时间**：20ms
- **短按范围**：50-500ms
- **响应延迟**：< 50ms
- **检测频率**：主循环频率（~10kHz）

### **电气特性**
- **工作电压**：3.3V
- **输入阻抗**：内部上拉电阻（~40kΩ）
- **低电平阈值**：< 0.8V
- **高电平阈值**：> 2.0V

## ✅ **验收测试**

### **功能测试清单**
- [ ] 按键按下时GPIO读取为低电平
- [ ] 按键释放时GPIO读取为高电平
- [ ] 短按能正确触发圈数累加
- [ ] 防抖功能正常，无误触发
- [ ] 3秒超时确认功能正常
- [ ] LED指示与按键状态同步

### **可靠性测试**
- [ ] 连续按键100次无异常
- [ ] 长时间运行无死机
- [ ] 环境温度变化下功能正常
- [ ] 电源电压波动下功能稳定

## 🎯 **总结**

**推荐配置：**
- ✅ 使用现有UserKEY引脚（GPIOA.18, Pin 11）
- ✅ 简单的两线连接（按键到GPIO和GND）
- ✅ 软件防抖，无需额外硬件
- ✅ 内部上拉电阻，电路简洁

**这个配置已经在代码中实现，只需要按照接线图连接一个轻触按键即可立即使用！**

---

## 📋 **快速接线参考**

```
开发板                    按键
┌─────────────┐          ┌─────┐
│             │          │     │
│  Pin 11 ────┼──────────┤  1  │
│ (GPIOA.18)  │          │     │
│             │          │  2  │
│   GND   ────┼──────────┤     │
│             │          └─────┘
└─────────────┘
```

**就是这么简单！连接完成后即可享受智能圈数控制功能！**
