# 外接LED接线指南

## 🔌 **硬件连接方案**

### **推荐配置：GPIOB.6 (Pin 58)**

**引脚信息：**
- **GPIO引脚**：`GPIOB.6`
- **物理引脚**：Package Pin 58
- **引脚名称**：HDPIN_5
- **配置**：输出模式，推挽输出

**接线方法：**
```
GPIOB.6 (Pin 58) ──── 330Ω电阻 ──── LED正极(长脚)
LED负极(短脚) ──── GND
```

## ⚡ **电路参数**

### **LED规格要求**
- **类型**：普通5mm LED或3mm LED
- **颜色**：红色、绿色、蓝色均可
- **正向电压**：1.8V-3.3V
- **正向电流**：10-20mA

### **限流电阻计算**
```
电阻值 = (VCC - LED正向电压) / LED电流
电阻值 = (3.3V - 2.0V) / 0.015A = 87Ω

推荐使用：330Ω（安全余量，电流约4mA）
```

## 🔧 **接线实施步骤**

### **第一步：准备材料**
- LED × 1（任意颜色）
- 330Ω电阻 × 1
- 杜邦线（公对母）× 2
- 面包板（可选）

### **第二步：物理连接**
1. **找到引脚**：在开发板上找到Package Pin 58（GPIOB.6）
2. **连接电路**：
   ```
   开发板Pin 58 ──── 330Ω电阻 ──── LED正极
   LED负极 ──── 开发板GND
   ```
3. **检查极性**：
   - LED长脚为正极，连接电阻
   - LED短脚为负极，连接GND

### **第三步：验证连接**
- 确保连接牢固，无短路
- 检查LED极性是否正确
- 确认电阻值为330Ω

## 📊 **电气特性**

### **GPIO输出特性**
- **输出电压**：0V（低电平）/ 3.3V（高电平）
- **输出电流**：最大20mA
- **驱动能力**：足够驱动单个LED

### **LED工作参数**
- **工作电流**：约4mA（使用330Ω电阻）
- **亮度**：中等亮度，清晰可见
- **功耗**：约13mW

## 🛡️ **安全注意事项**

### **电气安全**
- ✅ 必须使用限流电阻，防止LED烧毁
- ✅ 确认LED极性，反接不会亮
- ✅ 避免短路GPIO引脚到电源或地

### **机械安全**
- ✅ LED安装牢固，避免松动
- ✅ 导线长度适中，避免缠绕
- ✅ 预留足够的操作空间

## 🔍 **故障排除**

### **LED不亮**
**可能原因：**
- LED极性接反
- 电阻断路或接触不良
- GPIO配置错误
- LED损坏

**解决方法：**
1. 检查LED极性（长脚接正极）
2. 用万用表测试电阻和连接
3. 检查代码中的GPIO配置
4. 更换LED测试

### **LED很暗**
**可能原因：**
- 电阻值过大
- 电源电压不足
- LED老化

**解决方法：**
1. 减小电阻值（最小150Ω）
2. 检查电源电压
3. 更换新LED

### **LED过亮或发热**
**可能原因：**
- 电阻值过小或短路
- LED电流过大

**解决方法：**
1. 增大电阻值
2. 检查是否短路
3. 使用万用表测量电流

## 📋 **快速接线参考**

```
开发板                    LED电路
┌─────────────┐          ┌─────────────┐
│             │          │             │
│  Pin 58 ────┼──────────┤ 330Ω电阻   │
│ (GPIOB.6)   │          │      │      │
│             │          │   LED正极   │
│             │          │      │      │
│   GND   ────┼──────────┤   LED负极   │
│             │          │             │
└─────────────┘          └─────────────┘
```

## ✅ **验收测试**

### **功能测试清单**
- [ ] LED能正常点亮
- [ ] LED能正常熄灭
- [ ] LED亮度适中，清晰可见
- [ ] 无发热现象
- [ ] 连接牢固可靠

### **代码测试**
```c
// 测试代码示例
DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_6);    // LED亮
DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_6);  // LED灭
DL_GPIO_togglePins(GPIOB, DL_GPIO_PIN_6); // LED切换
```

## 🎯 **总结**

**推荐配置：**
- ✅ 使用GPIOB.6 (Pin 58)
- ✅ 330Ω限流电阻
- ✅ 普通5mm LED
- ✅ 简单的两线连接

**这个配置安全可靠，亮度适中，非常适合调试指示使用！**

---

**连接完成后，LED将作为系统状态指示灯，帮助调试按键和电机功能！**
