#include "system_time.h"

/* 系统时间变量 */
static volatile uint32_t g_system_time_ms = 0;
static volatile uint32_t g_loop_counter = 0;
static volatile uint32_t g_last_loop_counter = 0;

/* 时间基准配置 */
#define LOOP_FREQUENCY_HZ       10000   /* 假设主循环频率约10kHz */
#define MS_PER_LOOP_COUNT       100     /* 每100次循环约1ms */

/**
 * @brief 初始化系统时间模块
 */
void system_time_init(void)
{
    g_system_time_ms = 0;
    g_loop_counter = 0;
    g_last_loop_counter = 0;
}

/**
 * @brief 更新系统时间
 */
void system_time_update(void)
{
    g_loop_counter++;
    
    /* 每MS_PER_LOOP_COUNT次循环增加1ms */
    if((g_loop_counter - g_last_loop_counter) >= MS_PER_LOOP_COUNT) {
        g_system_time_ms++;
        g_last_loop_counter = g_loop_counter;
    }
}

/**
 * @brief 获取系统时间戳
 */
uint32_t system_time_get_ms(void)
{
    return g_system_time_ms;
}

/**
 * @brief 获取系统运行时间
 */
uint32_t system_time_get_seconds(void)
{
    return g_system_time_ms / 1000;
}

/**
 * @brief 时间差计算
 */
uint32_t system_time_diff_ms(uint32_t start_time, uint32_t end_time)
{
    /* 处理时间戳溢出情况 */
    if(end_time >= start_time) {
        return end_time - start_time;
    } else {
        /* 溢出情况：假设32位时间戳 */
        return (0xFFFFFFFF - start_time) + end_time + 1;
    }
}

/**
 * @brief 延时函数
 */
void system_time_delay_ms(uint32_t delay_ms)
{
    uint32_t start_time = system_time_get_ms();
    
    while(system_time_diff_ms(start_time, system_time_get_ms()) < delay_ms) {
        /* 更新时间基准 */
        system_time_update();
    }
}
